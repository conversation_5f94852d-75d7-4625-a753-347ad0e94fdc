import { Link } from 'react-router-dom';

export default function AmazonFooter() {
  const footerSections = [
    {
      title: 'Get to Know Us',
      links: [
        { name: 'Careers', href: '/careers' },
        { name: 'Blog', href: '/blog' },
        { name: 'About Amazon', href: '/about' },
        { name: 'Investor Relations', href: '/investor-relations' },
        { name: 'Amazon Devices', href: '/devices' },
        { name: 'Amazon Science', href: '/science' }
      ]
    },
    {
      title: 'Make Money with Us',
      links: [
        { name: 'Sell products on Amazon', href: '/sell' },
        { name: 'Sell on Amazon Business', href: '/business' },
        { name: 'Sell apps on Amazon', href: '/apps' },
        { name: 'Become an Affiliate', href: '/affiliate' },
        { name: 'Advertise Your Products', href: '/advertising' },
        { name: 'Self-Publish with Us', href: '/self-publish' }
      ]
    },
    {
      title: 'Amazon Payment Products',
      links: [
        { name: 'Amazon Business Card', href: '/business-card' },
        { name: 'Shop with Points', href: '/points' },
        { name: 'Reload Your Balance', href: '/reload' },
        { name: 'Amazon Currency Converter', href: '/currency' }
      ]
    },
    {
      title: 'Let Us Help You',
      links: [
        { name: 'Amazon and COVID-19', href: '/covid-19' },
        { name: 'Your Account', href: '/account' },
        { name: 'Your Orders', href: '/orders' },
        { name: 'Shipping Rates & Policies', href: '/shipping' },
        { name: 'Returns & Replacements', href: '/returns' },
        { name: 'Manage Your Content and Devices', href: '/manage-content' },
        { name: 'Amazon Assistant', href: '/assistant' },
        { name: 'Help', href: '/help' }
      ]
    }
  ];

  return (
    <footer className="bg-slate-800 text-white">
      {/* Back to Top */}
      <div className="bg-slate-700 hover:bg-slate-600 transition-colors">
        <button 
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="w-full py-4 text-center text-sm font-medium"
        >
          Back to top
        </button>
      </div>

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className="text-lg font-bold mb-4">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link 
                      to={link.href}
                      className="text-sm text-gray-300 hover:text-white hover:underline"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Divider */}
        <div className="border-t border-gray-600 my-8"></div>

        {/* Bottom Section */}
        <div className="flex flex-col md:flex-row items-center justify-between">
          {/* Logo */}
          <div className="flex items-center mb-4 md:mb-0">
            <Link to="/" className="text-2xl font-bold">
              <span className="bg-orange-400 text-black px-2 py-1 rounded">amazon</span>
            </Link>
          </div>

          {/* Language and Country */}
          <div className="flex items-center space-x-6">
            <button className="flex items-center space-x-2 text-sm border border-gray-600 rounded px-3 py-1 hover:bg-slate-700">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clipRule="evenodd" />
              </svg>
              <span>English</span>
            </button>

            <button className="flex items-center space-x-2 text-sm border border-gray-600 rounded px-3 py-1 hover:bg-slate-700">
              <span>💵</span>
              <span>USD - U.S. Dollar</span>
            </button>

            <button className="flex items-center space-x-2 text-sm border border-gray-600 rounded px-3 py-1 hover:bg-slate-700">
              <img src="https://flagcdn.com/w20/us.png" alt="US" className="w-5 h-3" />
              <span>United States</span>
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Links */}
      <div className="bg-slate-900 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 text-xs">
            <div>
              <h4 className="font-bold mb-2">Amazon Music</h4>
              <p className="text-gray-400">Stream millions of songs</p>
            </div>
            <div>
              <h4 className="font-bold mb-2">Amazon Advertising</h4>
              <p className="text-gray-400">Find, attract, and engage customers</p>
            </div>
            <div>
              <h4 className="font-bold mb-2">6pm</h4>
              <p className="text-gray-400">Score deals on fashion brands</p>
            </div>
            <div>
              <h4 className="font-bold mb-2">AbeBooks</h4>
              <p className="text-gray-400">Books, art & collectibles</p>
            </div>
            <div>
              <h4 className="font-bold mb-2">ACX</h4>
              <p className="text-gray-400">Audiobook Publishing Made Easy</p>
            </div>
            <div>
              <h4 className="font-bold mb-2">Sell on Amazon</h4>
              <p className="text-gray-400">Start a Selling Account</p>
            </div>
            <div>
              <h4 className="font-bold mb-2">Amazon Business</h4>
              <p className="text-gray-400">Everything For Your Business</p>
            </div>
          </div>

          {/* Copyright */}
          <div className="mt-8 pt-4 border-t border-gray-700 text-center text-xs text-gray-400">
            <p>&copy; 1996-2024, Amazon.com, Inc. or its affiliates</p>
            <div className="mt-2 space-x-4">
              <Link to="/conditions" className="hover:underline">Conditions of Use</Link>
              <Link to="/privacy" className="hover:underline">Privacy Notice</Link>
              <Link to="/interest-ads" className="hover:underline">Interest-Based Ads</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
