/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        amazon: {
          orange: '#ff9900',
          'orange-dark': '#e47911',
          blue: '#232f3e',
          'blue-light': '#37475a',
          yellow: '#febd69',
          gray: '#eaeded',
          'gray-dark': '#cccccc',
          text: '#0f1111',
          'text-light': '#565959',
          link: '#007185',
          'link-hover': '#c7511f',
          success: '#067d62',
          error: '#d13212',
        },
      },
      fontFamily: {
        'amazon': ['"Amazon Ember"', 'Arial', 'sans-serif'],
      },
      fontSize: {
        'amazon-xs': '12px',
        'amazon-sm': '14px',
        'amazon-base': '16px',
        'amazon-lg': '18px',
        'amazon-xl': '20px',
      },
      spacing: {
        'amazon-xs': '4px',
        'amazon-sm': '8px',
        'amazon-md': '16px',
        'amazon-lg': '24px',
        'amazon-xl': '32px',
      },
      borderRadius: {
        'amazon': '4px',
        'amazon-lg': '8px',
      },
      boxShadow: {
        'amazon': '0 2px 4px 0 rgba(0,0,0,.13)',
        'amazon-lg': '0 4px 8px 0 rgba(0,0,0,.13)',
        'amazon-hover': '0 4px 12px 0 rgba(0,0,0,.15)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
    },
  },
  plugins: [],
}
