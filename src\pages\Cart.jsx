import { Link } from 'react-router-dom';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import useCartStore from '../store/cartStore';

export default function Cart() {
  const { items, totalPrice, updateQuantity, removeItem, clearCart } = useCartStore();

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-100">
        <AmazonHeader />
        <AmazonNavigation />
        
        <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="mb-6">
              <svg className="mx-auto h-24 w-24 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5M7 13l-1.1 5m0 0h9.2M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Your Amazon Cart is empty</h2>
            <p className="text-gray-600 mb-6">Shop today's deals</p>
            <Link to="/" className="amazon-btn-primary">
              Continue shopping
            </Link>
          </div>
        </div>
        
        <AmazonFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />
      
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-bold text-gray-900">Shopping Cart</h1>
                <button 
                  onClick={clearCart}
                  className="text-sm text-blue-600 hover:underline"
                >
                  Clear cart
                </button>
              </div>

              <div className="space-y-6">
                {items.map((item) => (
                  <div key={item.id} className="flex items-start space-x-4 pb-6 border-b border-gray-200 last:border-b-0">
                    {/* Product Image */}
                    <div className="flex-shrink-0 w-24 h-24">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-full object-contain"
                      />
                    </div>

                    {/* Product Details */}
                    <div className="flex-1 min-w-0">
                      <Link to={`/product/${item.id}`} className="amazon-link">
                        <h3 className="text-lg font-medium text-gray-900 line-clamp-2">
                          {item.title}
                        </h3>
                      </Link>
                      
                      <div className="mt-2">
                        <span className="amazon-price text-xl">${item.price}</span>
                        {item.originalPrice && item.originalPrice > item.price && (
                          <span className="ml-2 text-sm text-gray-500 line-through">
                            ${item.originalPrice}
                          </span>
                        )}
                      </div>

                      {item.prime && (
                        <div className="mt-2">
                          <span className="text-blue-500 text-xs font-bold bg-blue-100 px-2 py-1 rounded">
                            prime
                          </span>
                        </div>
                      )}

                      <div className="mt-4 flex items-center space-x-4">
                        {/* Quantity Selector */}
                        <div className="flex items-center border border-gray-300 rounded">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="px-3 py-1 hover:bg-gray-100"
                          >
                            -
                          </button>
                          <span className="px-3 py-1 border-l border-r border-gray-300">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            className="px-3 py-1 hover:bg-gray-100"
                          >
                            +
                          </button>
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-sm text-blue-600 hover:underline"
                        >
                          Delete
                        </button>

                        {/* Save for Later */}
                        <button className="text-sm text-blue-600 hover:underline">
                          Save for later
                        </button>
                      </div>
                    </div>

                    {/* Item Total */}
                    <div className="text-right">
                      <div className="amazon-price text-lg">
                        ${(item.price * item.quantity).toFixed(2)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
              <h2 className="text-lg font-bold text-gray-900 mb-4">Order Summary</h2>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span>Subtotal ({items.reduce((total, item) => total + item.quantity, 0)} items):</span>
                  <span className="font-semibold">${totalPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping:</span>
                  <span className="text-green-600">FREE</span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span className="amazon-price">${totalPrice.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <button className="w-full amazon-btn-primary py-3 text-base mb-3">
                Proceed to checkout
              </button>

              <div className="text-xs text-gray-600 text-center">
                By placing your order, you agree to Amazon's{' '}
                <Link to="/conditions" className="amazon-link">
                  Conditions of Use
                </Link>{' '}
                and{' '}
                <Link to="/privacy" className="amazon-link">
                  Privacy Notice
                </Link>
                .
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <AmazonFooter />
    </div>
  );
}
