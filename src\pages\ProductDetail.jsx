import { usePara<PERSON>, <PERSON> } from 'react-router-dom';
import { useState } from 'react';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import useCartStore from '../store/cartStore';
import { mockProducts } from '../data/products';

export default function ProductDetail() {
  const { id } = useParams();
  const { addItem } = useCartStore();
  const [quantity, setQuantity] = useState(1);
  
  const product = mockProducts.find(p => p.id === parseInt(id));

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-100">
        <AmazonHeader />
        <AmazonNavigation />
        <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
            <Link to="/" className="amazon-btn-primary">
              Continue Shopping
            </Link>
          </div>
        </div>
        <AmazonFooter />
      </div>
    );
  }

  const handleAddToCart = () => {
    for (let i = 0; i < quantity; i++) {
      addItem(product);
    }
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }
    
    const emptyStars = 5 - fullStars;
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <svg key={`empty-${i}`} className="w-5 h-5 text-gray-300 fill-current" viewBox="0 0 20 20">
          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }
    
    return stars;
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />
      
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <nav className="mb-6">
          <ol className="flex items-center space-x-2 text-sm">
            <li><Link to="/" className="amazon-link">Home</Link></li>
            <li className="text-gray-500">/</li>
            <li><Link to={`/category/${product.category.toLowerCase()}`} className="amazon-link">{product.category}</Link></li>
            <li className="text-gray-500">/</li>
            <li className="text-gray-500 truncate">{product.title}</li>
          </ol>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Product Image */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-8 shadow-sm border border-gray-200">
              <img
                src={product.image}
                alt={product.title}
                className="w-full h-96 object-contain"
              />
            </div>
          </div>

          {/* Product Info */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">{product.title}</h1>
              
              {/* Rating */}
              <div className="flex items-center mb-4">
                <div className="flex items-center">
                  {renderStars(product.rating)}
                </div>
                <span className="ml-2 text-sm amazon-link">
                  {product.reviewCount.toLocaleString()} ratings
                </span>
              </div>

              {/* Price */}
              <div className="mb-6">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="amazon-price text-3xl">${product.price}</span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="text-lg text-gray-500 line-through">
                      ${product.originalPrice}
                    </span>
                  )}
                </div>
                {product.prime && (
                  <div className="flex items-center mb-2">
                    <span className="text-blue-500 text-sm font-bold bg-blue-100 px-2 py-1 rounded mr-2">
                      prime
                    </span>
                    <span className="text-sm text-gray-600">FREE One-Day Delivery</span>
                  </div>
                )}
                {product.freeShipping && (
                  <div className="text-sm text-green-600">
                    FREE Shipping on orders over $25
                  </div>
                )}
              </div>

              {/* Product Details */}
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">About this item</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• High-quality product with excellent reviews</li>
                    <li>• Fast and reliable shipping</li>
                    <li>• 30-day return policy</li>
                    <li>• Customer support available 24/7</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Purchase Options */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 sticky top-4">
              <div className="mb-4">
                <span className="amazon-price text-2xl">${product.price}</span>
                {product.freeShipping && (
                  <div className="text-sm text-green-600 mt-1">
                    FREE Shipping
                  </div>
                )}
              </div>

              <div className="mb-4">
                <span className="text-green-600 text-sm font-medium">In Stock</span>
              </div>

              {/* Quantity Selector */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity:
                </label>
                <select
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value))}
                  className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  {[...Array(10)].map((_, i) => (
                    <option key={i + 1} value={i + 1}>
                      {i + 1}
                    </option>
                  ))}
                </select>
              </div>

              {/* Add to Cart Button */}
              <button
                onClick={handleAddToCart}
                className="amazon-btn-cart w-full py-3 text-base mb-3"
              >
                Add to Cart
              </button>

              {/* Buy Now Button */}
              <button className="amazon-btn-primary w-full py-3 text-base mb-4">
                Buy Now
              </button>

              {/* Delivery Info */}
              <div className="text-sm text-gray-600 space-y-2">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  <span>Deliver to New York 10001</span>
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                  <span>Order within 2 hrs 15 mins</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <AmazonFooter />
    </div>
  );
}
