import { useState } from 'react';
import { Link } from 'react-router-dom';

export default function AmazonNavigation() {
  const [showAllMenu, setShowAllMenu] = useState(false);

  const categories = [
    'Electronics',
    'Books',
    'Clothing',
    'Home & Garden',
    'Sports',
    'Toys',
    'Automotive',
    'Health',
    'Beauty',
    'Grocery'
  ];

  return (
    <nav className="amazon-nav">
      <div className="flex items-center px-4 py-2 space-x-6">
        {/* All Menu */}
        <div className="relative">
          <button
            onClick={() => setShowAllMenu(!showAllMenu)}
            className="flex items-center text-white hover:bg-slate-600 px-3 py-1 rounded focus:outline-none"
          >
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
            All
          </button>

          {/* All Menu Dropdown */}
          {showAllMenu && (
            <div className="absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <div className="py-2">
                <div className="px-4 py-2 text-sm font-bold text-gray-900 border-b">
                  Shop by Department
                </div>
                {categories.map((category) => (
                  <Link
                    key={category}
                    to={`/category/${category.toLowerCase().replace(' & ', '-').replace(' ', '-')}`}
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => setShowAllMenu(false)}
                  >
                    {category}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Quick Links */}
        <Link to="/todays-deals" className="text-white hover:underline text-sm">
          Today's Deals
        </Link>
        
        <Link to="/customer-service" className="text-white hover:underline text-sm">
          Customer Service
        </Link>
        
        <Link to="/registry" className="text-white hover:underline text-sm">
          Registry
        </Link>
        
        <Link to="/gift-cards" className="text-white hover:underline text-sm">
          Gift Cards
        </Link>
        
        <Link to="/sell" className="text-white hover:underline text-sm">
          Sell
        </Link>

        {/* Prime */}
        <div className="flex items-center">
          <Link to="/prime" className="text-white hover:underline text-sm flex items-center">
            <span className="text-blue-400 font-bold mr-1">prime</span>
          </Link>
        </div>

        {/* Mobile Menu Toggle */}
        <div className="md:hidden ml-auto">
          <button className="text-white hover:bg-slate-600 p-2 rounded focus:outline-none">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Promotional Banner */}
      <div className="bg-slate-600 px-4 py-1">
        <div className="flex items-center justify-center text-white text-sm">
          <span className="mr-2">🎉</span>
          <span>Free shipping on orders over $25 shipped by Amazon</span>
          <Link to="/prime" className="ml-2 text-blue-300 hover:underline">
            Learn more
          </Link>
        </div>
      </div>
    </nav>
  );
}
