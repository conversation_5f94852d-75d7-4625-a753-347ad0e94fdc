import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isLoggedIn: false,
      isLoading: false,
      error: null,

      // Actions
      setLoading: (loading) => set({ isLoading: loading }),

      setError: (error) => set({ error }),

      clearError: () => set({ error: null }),

      login: (userData, token) => {
        set({
          user: userData,
          token,
          isLoggedIn: true,
          error: null,
          isLoading: false,
        });
        // Store token in localStorage for API calls
        localStorage.setItem('authToken', token);
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isLoggedIn: false,
          error: null,
          isLoading: false,
        });
        // Remove token from localStorage
        localStorage.removeItem('authToken');
      },

      // Initialize auth state from localStorage on app start
      initializeAuth: () => {
        const token = localStorage.getItem('authToken');
        if (token) {
          // In a real app, you'd validate the token with the server
          // For now, we'll assume it's valid if it exists
          set({
            token,
            isLoggedIn: true,
            // You might want to decode the token to get user info
            // or make an API call to get current user data
          });
        }
      },
    }),
    {
      name: 'auth-storage', // unique name for localStorage key
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isLoggedIn: state.isLoggedIn,
      }),
    }
  )
);

export default useAuthStore;
