export const mockProducts = [
  {
    id: 1,
    title: "Apple iPhone 15 Pro Max, 256GB, Natural Titanium",
    price: 1199.99,
    originalPrice: 1299.99,
    image: "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop",
    rating: 4.5,
    reviewCount: 12847,
    prime: true,
    freeShipping: true,
    category: "Electronics"
  },
  {
    id: 2,
    title: "Samsung 65\" QLED 4K Smart TV with Alexa Built-in",
    price: 899.99,
    originalPrice: 1199.99,
    image: "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=400&fit=crop",
    rating: 4.3,
    reviewCount: 8934,
    prime: true,
    freeShipping: true,
    category: "Electronics"
  },
  {
    id: 3,
    title: "Sony WH-1000XM5 Wireless Noise Canceling Headphones",
    price: 349.99,
    originalPrice: 399.99,
    image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop",
    rating: 4.7,
    reviewCount: 15623,
    prime: true,
    freeShipping: true,
    category: "Electronics"
  },
  {
    id: 4,
    title: "Amazon Echo Dot (5th Gen) Smart Speaker with <PERSON>a",
    price: 49.99,
    originalPrice: 59.99,
    image: "https://images.unsplash.com/photo-1543512214-318c7553f230?w=400&h=400&fit=crop",
    rating: 4.4,
    reviewCount: 45678,
    prime: true,
    freeShipping: true,
    category: "Electronics"
  },
  {
    id: 5,
    title: "Nike Air Max 270 Men's Running Shoes",
    price: 129.99,
    originalPrice: 150.00,
    image: "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop",
    rating: 4.2,
    reviewCount: 3456,
    prime: true,
    freeShipping: true,
    category: "Clothing & Shoes"
  },
  {
    id: 6,
    title: "Instant Pot Duo 7-in-1 Electric Pressure Cooker, 6 Quart",
    price: 79.99,
    originalPrice: 99.99,
    image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop",
    rating: 4.6,
    reviewCount: 67890,
    prime: true,
    freeShipping: true,
    category: "Home & Kitchen"
  },
  {
    id: 7,
    title: "The Psychology of Money by Morgan Housel",
    price: 14.99,
    originalPrice: 18.99,
    image: "https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=400&fit=crop",
    rating: 4.8,
    reviewCount: 23456,
    prime: true,
    freeShipping: false,
    category: "Books"
  },
  {
    id: 8,
    title: "LEGO Creator 3-in-1 Deep Sea Creatures Building Kit",
    price: 79.99,
    originalPrice: 89.99,
    image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
    rating: 4.5,
    reviewCount: 1234,
    prime: true,
    freeShipping: true,
    category: "Toys & Games"
  },
  {
    id: 9,
    title: "Keurig K-Classic Coffee Maker, Single Serve K-Cup Pod",
    price: 89.99,
    originalPrice: 119.99,
    image: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=400&fit=crop",
    rating: 4.3,
    reviewCount: 12345,
    prime: true,
    freeShipping: true,
    category: "Home & Kitchen"
  },
  {
    id: 10,
    title: "Fitbit Charge 5 Advanced Fitness & Health Tracker",
    price: 149.99,
    originalPrice: 199.99,
    image: "https://images.unsplash.com/photo-*************-040b8e1fd5b6?w=400&h=400&fit=crop",
    rating: 4.1,
    reviewCount: 8765,
    prime: true,
    freeShipping: true,
    category: "Sports & Outdoors"
  },
  {
    id: 11,
    title: "Anker Portable Charger, PowerCore 10000mAh Power Bank",
    price: 24.99,
    originalPrice: 29.99,
    image: "https://images.unsplash.com/photo-*************-4b0b2b0b2b0b?w=400&h=400&fit=crop",
    rating: 4.4,
    reviewCount: 34567,
    prime: true,
    freeShipping: true,
    category: "Electronics"
  },
  {
    id: 12,
    title: "Hydro Flask Water Bottle, Stainless Steel & Vacuum Insulated",
    price: 39.99,
    originalPrice: 44.99,
    image: "https://images.unsplash.com/photo-*************-7111542de6e8?w=400&h=400&fit=crop",
    rating: 4.6,
    reviewCount: 5678,
    prime: true,
    freeShipping: true,
    category: "Sports & Outdoors"
  }
];

export const featuredProducts = mockProducts.slice(0, 4);
export const todaysDeals = mockProducts.filter(product => product.originalPrice > product.price);
export const recommendedProducts = mockProducts.slice(4, 8);
export const newArrivals = mockProducts.slice(8, 12);
