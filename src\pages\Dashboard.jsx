import { Link } from 'react-router-dom';
import useAuthStore from '../store/authStore';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';

export default function Dashboard() {
  const { user, logout } = useAuthStore();

  const quickActions = [
    {
      title: 'Your Orders',
      description: 'Track, return, or buy things again',
      icon: '📦',
      href: '/orders',
      color: 'bg-blue-50 hover:bg-blue-100'
    },
    {
      title: 'Login & security',
      description: 'Edit login, name, and mobile number',
      icon: '🔒',
      href: '/account/security',
      color: 'bg-green-50 hover:bg-green-100'
    },
    {
      title: 'Prime',
      description: 'View benefits and payment settings',
      icon: '⭐',
      href: '/prime',
      color: 'bg-yellow-50 hover:bg-yellow-100'
    },
    {
      title: 'Your Addresses',
      description: 'Edit addresses for orders and gifts',
      icon: '📍',
      href: '/addresses',
      color: 'bg-purple-50 hover:bg-purple-100'
    },
    {
      title: 'Payment options',
      description: 'Edit or add payment methods',
      icon: '💳',
      href: '/payment',
      color: 'bg-red-50 hover:bg-red-100'
    },
    {
      title: 'Your Lists',
      description: 'View, modify, and share your lists',
      icon: '📝',
      href: '/lists',
      color: 'bg-indigo-50 hover:bg-indigo-100'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Amazon Header */}
      <AmazonHeader />

      {/* Amazon Navigation */}
      <AmazonNavigation />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Your Account
          </h1>
          <p className="text-gray-600">
            Hello, {user?.name}! Manage your account settings and view your activity.
          </p>
        </div>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              to={action.href}
              className={`${action.color} p-6 rounded-lg border border-gray-200 transition-colors duration-200 block`}
            >
              <div className="flex items-start space-x-4">
                <div className="text-3xl">{action.icon}</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {action.title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {action.description}
                  </p>
                </div>
                <div className="text-gray-400">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Account Overview Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Account Info Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Account Information</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Name:</span>
                <span className="font-medium text-gray-900">{user?.name}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Email:</span>
                <span className="font-medium text-gray-900">{user?.email}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Account Type:</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user?.role === 'admin'
                    ? 'bg-purple-100 text-purple-800'
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {user?.role === 'admin' ? 'Administrator' : 'Customer'}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-600">Member since:</span>
                <span className="font-medium text-gray-900">January 2024</span>
              </div>
            </div>
          </div>

          {/* Recent Activity Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Activity</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 py-3 border-b border-gray-100">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Account created successfully</p>
                  <p className="text-xs text-gray-500">Welcome to Amazon!</p>
                </div>
                <span className="text-xs text-gray-400">Today</span>
              </div>

              <div className="flex items-center space-x-3 py-3 border-b border-gray-100">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Profile updated</p>
                  <p className="text-xs text-gray-500">Your account information has been updated</p>
                </div>
                <span className="text-xs text-gray-400">2 days ago</span>
              </div>

              <div className="flex items-center space-x-3 py-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Security reminder</p>
                  <p className="text-xs text-gray-500">Consider enabling two-factor authentication</p>
                </div>
                <span className="text-xs text-gray-400">1 week ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Links Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Quick Links</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link to="/orders" className="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">📦</div>
              <div className="text-sm font-medium text-gray-900">Your Orders</div>
            </Link>
            <Link to="/cart" className="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">🛒</div>
              <div className="text-sm font-medium text-gray-900">Your Cart</div>
            </Link>
            <Link to="/lists" className="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">❤️</div>
              <div className="text-sm font-medium text-gray-900">Your Lists</div>
            </Link>
            <button
              onClick={logout}
              className="text-center p-4 rounded-lg hover:bg-red-50 transition-colors text-red-600"
            >
              <div className="text-2xl mb-2">🚪</div>
              <div className="text-sm font-medium">Sign Out</div>
            </button>
          </div>
        </div>

        {/* Back to Shopping */}
        <div className="text-center">
          <Link
            to="/"
            className="amazon-btn-primary inline-flex items-center px-6 py-3"
          >
            <svg className="mr-2 -ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Continue Shopping
          </Link>
        </div>
      </div>

      {/* Amazon Footer */}
      <AmazonFooter />
    </div>
  );
}
