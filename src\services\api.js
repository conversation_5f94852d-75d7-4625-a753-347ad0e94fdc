import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/api', // This would be your actual API base URL
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, logout user
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Mock authentication functions (simulating API calls)
export const authAPI = {
  // Mock login function
  login: async (credentials) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const { email, password } = credentials;

    // Mock validation - in real app, this would be server-side
    if (email === '<EMAIL>' && password === 'password123') {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin',
      };
      const mockToken = 'mock-jwt-token-' + Date.now();

      return {
        data: {
          user: mockUser,
          token: mockToken,
          message: 'Login successful',
        },
      };
    } else if (email === '<EMAIL>' && password === 'password123') {
      const mockUser = {
        id: 2,
        email: '<EMAIL>',
        name: 'Regular User',
        role: 'user',
      };
      const mockToken = 'mock-jwt-token-' + Date.now();

      return {
        data: {
          user: mockUser,
          token: mockToken,
          message: 'Login successful',
        },
      };
    } else {
      throw new Error('Invalid credentials');
    }
  },

  // Mock register function
  register: async (userData) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1200));

    const { email, password, name } = userData;

    // Mock validation
    if (!email || !password || !name) {
      throw new Error('All fields are required');
    }

    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }

    // Simulate email already exists check
    if (email === '<EMAIL>' || email === '<EMAIL>') {
      throw new Error('Email already exists');
    }

    const mockUser = {
      id: Date.now(),
      email,
      name,
      role: 'user',
    };
    const mockToken = 'mock-jwt-token-' + Date.now();

    return {
      data: {
        user: mockUser,
        token: mockToken,
        message: 'Registration successful',
      },
    };
  },

  // Mock logout function
  logout: async () => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      data: {
        message: 'Logout successful',
      },
    };
  },

  // Mock function to get current user (for token validation)
  getCurrentUser: async () => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    const token = localStorage.getItem('authToken');
    if (!token) {
      throw new Error('No token found');
    }

    // Mock user data based on token (in real app, server would validate token)
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
    };

    return {
      data: {
        user: mockUser,
      },
    };
  },
};

export default api;
