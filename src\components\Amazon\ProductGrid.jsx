import ProductCard from './ProductCard';

export default function ProductGrid({ products, title, showMore = false }) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      {/* Section Title */}
      {title && (
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">{title}</h2>
          {showMore && (
            <button className="amazon-link text-sm">
              See more
            </button>
          )}
        </div>
      )}

      {/* Products Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}
