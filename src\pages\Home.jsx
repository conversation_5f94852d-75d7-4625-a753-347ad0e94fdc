import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import ProductGrid from '../components/Amazon/ProductGrid';
import { featuredProducts, todaysDeals, recommendedProducts, newArrivals } from '../data/products';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Amazon Header */}
      <AmazonHeader />

      {/* Amazon Navigation */}
      <AmazonNavigation />

      {/* Hero Banner */}
      <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold sm:text-5xl md:text-6xl">
              Great deals on everything
            </h1>
            <p className="mt-4 text-xl sm:text-2xl">
              Shop millions of products with fast, free delivery
            </p>
            <div className="mt-8">
              <button className="amazon-btn-primary text-lg px-8 py-3">
                Shop now
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="space-y-8">
          {/* Today's Deals */}
          <ProductGrid
            products={todaysDeals}
            title="Today's Deals"
            showMore={true}
          />

          {/* Featured Products */}
          <ProductGrid
            products={featuredProducts}
            title="Featured Products"
            showMore={true}
          />

          {/* Recommended for You */}
          <ProductGrid
            products={recommendedProducts}
            title="Recommended for you"
            showMore={true}
          />

          {/* New Arrivals */}
          <ProductGrid
            products={newArrivals}
            title="New Arrivals"
            showMore={true}
          />
        </div>
      </div>

      {/* Categories Section */}
      <div className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Shop by Category</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {[
              { name: 'Electronics', icon: '📱', color: 'bg-blue-100' },
              { name: 'Books', icon: '📚', color: 'bg-green-100' },
              { name: 'Clothing', icon: '👕', color: 'bg-purple-100' },
              { name: 'Home', icon: '🏠', color: 'bg-yellow-100' },
              { name: 'Sports', icon: '⚽', color: 'bg-red-100' },
              { name: 'Toys', icon: '🧸', color: 'bg-pink-100' }
            ].map((category) => (
              <div key={category.name} className={`${category.color} p-6 rounded-lg text-center hover:shadow-md transition-shadow cursor-pointer`}>
                <div className="text-4xl mb-2">{category.icon}</div>
                <h3 className="font-semibold text-gray-900">{category.name}</h3>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Amazon Footer */}
      <AmazonFooter />
    </div>
  );
}
