@import "tailwindcss";

/* Tailwind CSS v4 Theme Configuration */
@theme {
  --color-amazon-orange: #ff9900;
  --color-amazon-orange-dark: #e47911;
  --color-amazon-blue: #232f3e;
  --color-amazon-blue-light: #37475a;
  --color-amazon-yellow: #febd69;
  --color-amazon-gray: #eaeded;
  --color-amazon-gray-dark: #cccccc;
  --color-amazon-text: #0f1111;
  --color-amazon-text-light: #565959;
  --color-amazon-link: #007185;
  --color-amazon-link-hover: #c7511f;
  --color-amazon-success: #067d62;
  --color-amazon-error: #d13212;

  --font-family-amazon: "Amazon Ember", Arial, sans-serif;

  --font-size-amazon-xs: 12px;
  --font-size-amazon-sm: 14px;
  --font-size-amazon-base: 16px;
  --font-size-amazon-lg: 18px;
  --font-size-amazon-xl: 20px;

  --spacing-amazon-xs: 4px;
  --spacing-amazon-sm: 8px;
  --spacing-amazon-md: 16px;
  --spacing-amazon-lg: 24px;
  --spacing-amazon-xl: 32px;

  --radius-amazon: 4px;
  --radius-amazon-lg: 8px;

  --shadow-amazon: 0 2px 4px 0 rgba(0,0,0,.13);
  --shadow-amazon-lg: 0 4px 8px 0 rgba(0,0,0,.13);
  --shadow-amazon-hover: 0 4px 12px 0 rgba(0,0,0,.15);

  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-in: slideIn 0.3s ease-out;
}

/* Custom base styles with Amazon theme */
html {
  font-family: var(--font-family-amazon);
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #ffffff;
  color: var(--color-amazon-text);
  font-size: var(--font-size-amazon-sm);
  line-height: 1.4;
}

/* Amazon-style component styles */
/* Amazon Buttons */
.amazon-btn-primary {
  background-color: var(--color-amazon-yellow);
  color: black;
  padding: 8px 16px;
  border-radius: var(--radius-amazon);
  font-size: var(--font-size-amazon-sm);
  font-weight: 500;
  border: 1px solid var(--color-amazon-orange-dark);
  box-shadow: var(--shadow-amazon);
  transition: all 0.2s;
  cursor: pointer;
}

.amazon-btn-primary:hover {
  background-color: #e6b800;
  box-shadow: var(--shadow-amazon-hover);
}

.amazon-btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.amazon-btn-secondary {
  background-color: #f3f4f6;
  color: var(--color-amazon-text);
  padding: 8px 16px;
  border-radius: var(--radius-amazon);
  font-size: var(--font-size-amazon-sm);
  font-weight: 500;
  border: 1px solid var(--color-amazon-gray-dark);
  transition: all 0.2s;
  cursor: pointer;
}

.amazon-btn-secondary:hover {
  background-color: #e5e7eb;
}

.amazon-btn-cart {
  background-color: var(--color-amazon-orange);
  color: black;
  padding: 8px 24px;
  border-radius: 9999px;
  font-size: var(--font-size-amazon-sm);
  font-weight: 500;
  border: 1px solid var(--color-amazon-orange-dark);
  box-shadow: var(--shadow-amazon);
  transition: all 0.2s;
  cursor: pointer;
}

.amazon-btn-cart:hover {
  background-color: var(--color-amazon-orange-dark);
  box-shadow: var(--shadow-amazon-hover);
}

/* Amazon Search Bar */
.amazon-search-input {
  width: 100%;
  padding: 8px 12px;
  border: 2px solid var(--color-amazon-orange);
  border-top-left-radius: var(--radius-amazon);
  border-bottom-left-radius: var(--radius-amazon);
  font-size: var(--font-size-amazon-base);
  transition: border-color 0.2s;
}

.amazon-search-input:focus {
  outline: none;
  border-color: var(--color-amazon-orange-dark);
}

.amazon-search-btn {
  background-color: var(--color-amazon-orange);
  padding: 8px 16px;
  border: 2px solid var(--color-amazon-orange);
  border-top-right-radius: var(--radius-amazon);
  border-bottom-right-radius: var(--radius-amazon);
  transition: background-color 0.2s;
  cursor: pointer;
}

.amazon-search-btn:hover {
  background-color: var(--color-amazon-orange-dark);
}

/* Amazon Cards */
.amazon-card {
  background-color: white;
  border: 1px solid var(--color-amazon-gray);
  border-radius: var(--radius-amazon-lg);
  box-shadow: var(--shadow-amazon);
  transition: box-shadow 0.2s;
}

.amazon-card:hover {
  box-shadow: var(--shadow-amazon-hover);
}

.amazon-product-card {
  background-color: white;
  border: 1px solid var(--color-amazon-gray);
  border-radius: var(--radius-amazon-lg);
  padding: 16px;
  box-shadow: var(--shadow-amazon);
  transition: box-shadow 0.2s;
  cursor: pointer;
}

.amazon-product-card:hover {
  box-shadow: var(--shadow-amazon-lg);
}

/* Amazon Header */
.amazon-header {
  background-color: var(--color-amazon-blue);
  color: white;
}

.amazon-nav {
  background-color: var(--color-amazon-blue-light);
  color: white;
}

/* Amazon Links */
.amazon-link {
  color: var(--color-amazon-link);
  transition: color 0.2s;
}

.amazon-link:hover {
  color: var(--color-amazon-link-hover);
  text-decoration: underline;
}

/* Amazon Price */
.amazon-price {
  color: var(--color-amazon-error);
  font-weight: bold;
  font-size: var(--font-size-amazon-lg);
}

.amazon-price-small {
  color: var(--color-amazon-error);
  font-weight: 600;
  font-size: var(--font-size-amazon-sm);
}

/* Amazon Dashboard Cards */
.amazon-dashboard-card {
  background-color: white;
  border-radius: var(--radius-amazon-lg);
  box-shadow: var(--shadow-amazon);
  border: 1px solid var(--color-amazon-gray);
  padding: 24px;
  transition: box-shadow 0.2s;
}

.amazon-dashboard-card:hover {
  box-shadow: var(--shadow-amazon-hover);
}

/* Amazon Quick Action Cards */
.amazon-quick-action {
  padding: 24px;
  border-radius: var(--radius-amazon-lg);
  border: 1px solid var(--color-amazon-gray);
  transition: box-shadow 0.2s;
  display: block;
}

.amazon-quick-action:hover {
  box-shadow: var(--shadow-amazon-hover);
}

/* Amazon Activity Item */
.amazon-activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-amazon-gray);
}

.amazon-activity-item:last-child {
  border-bottom: none;
}

/* Amazon Section Header */
.amazon-section-header {
  font-size: var(--font-size-amazon-xl);
  font-weight: bold;
  color: var(--color-amazon-text);
  margin-bottom: 16px;
}

/* Amazon Info Row */
.amazon-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--color-amazon-gray);
}

.amazon-info-row:last-child {
  border-bottom: none;
}

/* Custom utility styles */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  border-top-color: #4f46e5;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

*:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
