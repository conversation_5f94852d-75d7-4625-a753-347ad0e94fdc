@tailwind base;
@tailwind components;
@tailwind utilities;

/* Amazon Color Variables */
:root {
  --amazon-orange: #ff9900;
  --amazon-orange-dark: #e47911;
  --amazon-blue: #232f3e;
  --amazon-blue-light: #37475a;
  --amazon-yellow: #febd69;
  --amazon-gray: #eaeded;
  --amazon-gray-dark: #cccccc;
  --amazon-text: #0f1111;
  --amazon-text-light: #565959;
  --amazon-link: #007185;
  --amazon-link-hover: #c7511f;
  --amazon-success: #067d62;
  --amazon-error: #d13212;
}

/* Custom base styles */
@layer base {
  html {
    font-family: "Amazon Ember", Arial, sans-serif;
  }

  body {
    margin: 0;
    min-height: 100vh;
    background-color: #ffffff;
    color: var(--amazon-text);
    font-size: 14px;
    line-height: 1.4;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
  }

  .form-input {
    @apply appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
  }

  .form-input-error {
    @apply border-red-300 focus:ring-red-500 focus:border-red-500;
  }

  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }

  .card-header {
    @apply px-4 py-5 sm:px-6;
  }

  .card-body {
    @apply px-4 py-5 sm:p-6;
  }

  .card-footer {
    @apply bg-gray-50 px-4 py-4 sm:px-6;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-indigo-600;
  }
}

/* Focus styles for accessibility */
@layer base {
  *:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  *:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
