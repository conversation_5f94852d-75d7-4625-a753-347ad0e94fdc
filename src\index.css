@tailwind base;
@tailwind components;
@tailwind utilities;

/* Amazon Color Variables */
:root {
  --amazon-orange: #ff9900;
  --amazon-orange-dark: #e47911;
  --amazon-blue: #232f3e;
  --amazon-blue-light: #37475a;
  --amazon-yellow: #febd69;
  --amazon-gray: #eaeded;
  --amazon-gray-dark: #cccccc;
  --amazon-text: #0f1111;
  --amazon-text-light: #565959;
  --amazon-link: #007185;
  --amazon-link-hover: #c7511f;
  --amazon-success: #067d62;
  --amazon-error: #d13212;
}

/* Custom base styles with Amazon theme */
@layer base {
  html {
    font-family: "Amazon Ember", Arial, sans-serif;
  }

  body {
    margin: 0;
    min-height: 100vh;
    background-color: #ffffff;
    color: var(--amazon-text);
    font-size: 14px;
    line-height: 1.4;
  }
}

/* Amazon-style component styles */
@layer components {
  /* Amazon Buttons */
  .amazon-btn-primary {
    @apply bg-amazon-yellow hover:bg-yellow-500 text-black px-4 py-2 rounded-amazon text-sm font-medium border border-amazon-orange-dark focus:outline-none focus:ring-2 focus:ring-amazon-orange disabled:opacity-50 disabled:cursor-not-allowed shadow-amazon hover:shadow-amazon-hover transition-all duration-200;
  }

  .amazon-btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-amazon-text px-4 py-2 rounded-amazon text-sm font-medium border border-amazon-gray-dark focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200;
  }

  .amazon-btn-cart {
    @apply bg-amazon-orange hover:bg-amazon-orange-dark text-black px-6 py-2 rounded-full text-sm font-medium border border-amazon-orange-dark focus:outline-none focus:ring-2 focus:ring-amazon-orange shadow-amazon hover:shadow-amazon-hover transition-all duration-200;
  }

  /* Amazon Search Bar */
  .amazon-search-input {
    @apply w-full px-3 py-2 border-2 border-amazon-orange rounded-l-amazon focus:outline-none focus:border-amazon-orange-dark text-base transition-colors duration-200;
  }

  .amazon-search-btn {
    @apply bg-amazon-orange hover:bg-amazon-orange-dark px-4 py-2 border-2 border-amazon-orange rounded-r-amazon focus:outline-none transition-colors duration-200;
  }

  /* Amazon Cards */
  .amazon-card {
    @apply bg-white border border-amazon-gray rounded-amazon-lg shadow-amazon hover:shadow-amazon-hover transition-all duration-200;
  }

  .amazon-product-card {
    @apply bg-white border border-amazon-gray rounded-amazon-lg p-4 shadow-amazon hover:shadow-amazon-lg transition-all duration-200 cursor-pointer;
  }

  /* Amazon Header */
  .amazon-header {
    @apply bg-amazon-blue text-white;
  }

  .amazon-nav {
    @apply bg-amazon-blue-light text-white;
  }

  /* Amazon Links */
  .amazon-link {
    @apply text-amazon-link hover:text-amazon-link-hover hover:underline transition-colors duration-200;
  }

  /* Amazon Price */
  .amazon-price {
    @apply text-amazon-error font-bold text-lg;
  }

  .amazon-price-small {
    @apply text-amazon-error font-semibold text-sm;
  }

  /* Amazon Dashboard Cards */
  .amazon-dashboard-card {
    @apply bg-white rounded-amazon-lg shadow-amazon border border-amazon-gray p-6 hover:shadow-amazon-hover transition-all duration-200;
  }

  /* Amazon Quick Action Cards */
  .amazon-quick-action {
    @apply p-6 rounded-amazon-lg border border-amazon-gray transition-all duration-200 block hover:shadow-amazon-hover;
  }

  /* Amazon Activity Item */
  .amazon-activity-item {
    @apply flex items-center space-x-3 py-3 border-b border-amazon-gray last:border-b-0;
  }

  /* Amazon Section Header */
  .amazon-section-header {
    @apply text-xl font-bold text-amazon-text mb-4;
  }

  /* Amazon Info Row */
  .amazon-info-row {
    @apply flex justify-between items-center py-2 border-b border-amazon-gray last:border-b-0;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-indigo-600;
  }
}

/* Focus styles for accessibility */
@layer base {
  *:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  *:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
