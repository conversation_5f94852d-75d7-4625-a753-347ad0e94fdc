@tailwind base;
@tailwind components;
@tailwind utilities;

/* Amazon Color Variables */
:root {
  --color-amazon-orange: #ff9900;
  --color-amazon-orange-dark: #e47911;
  --color-amazon-blue: #232f3e;
  --color-amazon-blue-light: #37475a;
  --color-amazon-yellow: #febd69;
  --color-amazon-gray: #eaeded;
  --color-amazon-gray-dark: #cccccc;
  --color-amazon-text: #0f1111;
  --color-amazon-text-light: #565959;
  --color-amazon-link: #007185;
  --color-amazon-link-hover: #c7511f;
  --color-amazon-success: #067d62;
  --color-amazon-error: #d13212;

  --font-family-amazon: "Amazon Ember", Arial, sans-serif;

  --font-size-amazon-xs: 12px;
  --font-size-amazon-sm: 14px;
  --font-size-amazon-base: 16px;
  --font-size-amazon-lg: 18px;
  --font-size-amazon-xl: 20px;

  --spacing-amazon-xs: 4px;
  --spacing-amazon-sm: 8px;
  --spacing-amazon-md: 16px;
  --spacing-amazon-lg: 24px;
  --spacing-amazon-xl: 32px;

  --radius-amazon: 4px;
  --radius-amazon-lg: 8px;

  --shadow-amazon: 0 2px 4px 0 rgba(0,0,0,.13);
  --shadow-amazon-lg: 0 4px 8px 0 rgba(0,0,0,.13);
  --shadow-amazon-hover: 0 4px 12px 0 rgba(0,0,0,.15);

  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-in: slideIn 0.3s ease-out;
}

/* Custom base styles with Amazon theme */
html {
  font-family: var(--font-family-amazon);
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #ffffff;
  color: var(--color-amazon-text);
  font-size: var(--font-size-amazon-sm);
  line-height: 1.4;
}

/* Amazon-style component styles */
@layer components {
  /* Amazon Buttons */
  .amazon-btn-primary {
    @apply bg-amazon-yellow hover:bg-yellow-500 text-black px-4 py-2 rounded-amazon text-amazon-sm font-medium border border-amazon-orange-dark focus:outline-none focus:ring-2 focus:ring-amazon-orange disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200;
    box-shadow: var(--shadow-amazon);
  }

  .amazon-btn-primary:hover {
    box-shadow: var(--shadow-amazon-hover);
  }

  .amazon-btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-amazon-text px-4 py-2 rounded-amazon text-amazon-sm font-medium border border-amazon-gray-dark focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200;
  }

  .amazon-btn-cart {
    @apply bg-amazon-orange hover:bg-amazon-orange-dark text-black px-6 py-2 rounded-full text-amazon-sm font-medium border border-amazon-orange-dark focus:outline-none focus:ring-2 focus:ring-amazon-orange transition-all duration-200;
    box-shadow: var(--shadow-amazon);
  }

  .amazon-btn-cart:hover {
    box-shadow: var(--shadow-amazon-hover);
  }

  /* Amazon Search Bar */
  .amazon-search-input {
    @apply w-full px-3 py-2 border-2 border-amazon-orange rounded-l-amazon focus:outline-none focus:border-amazon-orange-dark text-amazon-base transition-colors duration-200;
  }

  .amazon-search-btn {
    @apply bg-amazon-orange hover:bg-amazon-orange-dark px-4 py-2 border-2 border-amazon-orange rounded-r-amazon focus:outline-none transition-colors duration-200;
  }

  /* Amazon Cards */
  .amazon-card {
    @apply bg-white border border-amazon-gray rounded-amazon-lg transition-shadow duration-200;
    box-shadow: var(--shadow-amazon);
  }

  .amazon-card:hover {
    box-shadow: var(--shadow-amazon-hover);
  }

  .amazon-product-card {
    @apply bg-white border border-amazon-gray rounded-amazon-lg p-4 cursor-pointer transition-shadow duration-200;
    box-shadow: var(--shadow-amazon);
  }

  .amazon-product-card:hover {
    box-shadow: var(--shadow-amazon-lg);
  }

  /* Amazon Header */
  .amazon-header {
    @apply bg-amazon-blue text-white;
  }

  .amazon-nav {
    @apply bg-amazon-blue-light text-white;
  }

  /* Amazon Links */
  .amazon-link {
    @apply text-amazon-link hover:text-amazon-link-hover hover:underline transition-colors duration-200;
  }

  /* Amazon Price */
  .amazon-price {
    @apply text-amazon-error font-bold text-amazon-lg;
  }

  .amazon-price-small {
    @apply text-amazon-error font-semibold text-amazon-sm;
  }

  /* Amazon Dashboard Cards */
  .amazon-dashboard-card {
    @apply bg-white rounded-amazon-lg border border-amazon-gray p-6 transition-shadow duration-200;
    box-shadow: var(--shadow-amazon);
  }

  .amazon-dashboard-card:hover {
    box-shadow: var(--shadow-amazon-hover);
  }

  /* Amazon Quick Action Cards */
  .amazon-quick-action {
    @apply p-6 rounded-amazon-lg border border-amazon-gray transition-shadow duration-200 block;
  }

  .amazon-quick-action:hover {
    box-shadow: var(--shadow-amazon-hover);
  }

  /* Amazon Activity Item */
  .amazon-activity-item {
    @apply flex items-center gap-3 py-3 border-b border-amazon-gray last:border-b-0;
  }

  /* Amazon Section Header */
  .amazon-section-header {
    @apply text-amazon-xl font-bold text-amazon-text mb-4;
  }

  /* Amazon Info Row */
  .amazon-info-row {
    @apply flex justify-between items-center py-2 border-b border-amazon-gray last:border-b-0;
  }
}

/* Custom utility styles */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  border-top-color: #4f46e5;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

*:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
