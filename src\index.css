@tailwind base;
@tailwind components;
@tailwind utilities;

/* Amazon Color Variables */
:root {
  --amazon-orange: #ff9900;
  --amazon-orange-dark: #e47911;
  --amazon-blue: #232f3e;
  --amazon-blue-light: #37475a;
  --amazon-yellow: #febd69;
  --amazon-gray: #eaeded;
  --amazon-gray-dark: #cccccc;
  --amazon-text: #0f1111;
  --amazon-text-light: #565959;
  --amazon-link: #007185;
  --amazon-link-hover: #c7511f;
  --amazon-success: #067d62;
  --amazon-error: #d13212;
}

/* Custom base styles */
@layer base {
  html {
    font-family: "Amazon Ember", Arial, sans-serif;
  }

  body {
    margin: 0;
    min-height: 100vh;
    background-color: #ffffff;
    color: var(--amazon-text);
    font-size: 14px;
    line-height: 1.4;
  }
}

/* Amazon-style component styles */
@layer components {
  /* Amazon Buttons */
  .amazon-btn-primary {
    @apply bg-yellow-400 hover:bg-yellow-500 text-black px-4 py-2 rounded-md text-sm font-medium border border-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .amazon-btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-900 px-4 py-2 rounded-md text-sm font-medium border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500;
  }

  .amazon-btn-cart {
    @apply bg-orange-400 hover:bg-orange-500 text-black px-6 py-2 rounded-full text-sm font-medium border border-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500;
  }

  /* Amazon Search Bar */
  .amazon-search-input {
    @apply w-full px-3 py-2 border-2 border-orange-400 rounded-l-md focus:outline-none focus:border-orange-500 text-base;
  }

  .amazon-search-btn {
    @apply bg-orange-400 hover:bg-orange-500 px-4 py-2 border-2 border-orange-400 rounded-r-md focus:outline-none;
  }

  /* Amazon Cards */
  .amazon-card {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  .amazon-product-card {
    @apply bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow duration-200 cursor-pointer;
  }

  /* Amazon Header */
  .amazon-header {
    @apply bg-slate-800 text-white;
  }

  .amazon-nav {
    @apply bg-slate-700 text-white;
  }

  /* Amazon Links */
  .amazon-link {
    @apply text-blue-600 hover:text-orange-600 hover:underline;
  }

  /* Amazon Price */
  .amazon-price {
    @apply text-red-600 font-bold text-lg;
  }

  .amazon-price-small {
    @apply text-red-600 font-semibold text-sm;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-indigo-600;
  }
}

/* Focus styles for accessibility */
@layer base {
  *:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  *:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
