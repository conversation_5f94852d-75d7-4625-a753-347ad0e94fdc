import { Link } from 'react-router-dom';
import useCartStore from '../../store/cartStore';

export default function ProductCard({ product }) {
  const { addItem } = useCartStore();
  const {
    id,
    title,
    price,
    originalPrice,
    image,
    rating,
    reviewCount,
    prime,
    freeShipping,
    category
  } = product;

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }

    if (hasHalfStar) {
      stars.push(
        <svg key="half" className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="half">
              <stop offset="50%" stopColor="currentColor" />
              <stop offset="50%" stopColor="transparent" />
            </linearGradient>
          </defs>
          <path fill="url(#half)" d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <svg key={`empty-${i}`} className="w-4 h-4 text-gray-300 fill-current" viewBox="0 0 20 20">
          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }

    return stars;
  };

  return (
    <div className="amazon-product-card group">
      <Link to={`/product/${id}`}>
        {/* Product Image */}
        <div className="aspect-square mb-3 overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-200"
          />
        </div>

        {/* Product Title */}
        <h3 className="text-sm text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600">
          {title}
        </h3>

        {/* Rating */}
        <div className="flex items-center mb-2">
          <div className="flex items-center">
            {renderStars(rating)}
          </div>
          <span className="ml-2 text-sm text-blue-600 hover:underline">
            {reviewCount.toLocaleString()}
          </span>
        </div>

        {/* Price */}
        <div className="mb-2">
          <div className="flex items-center space-x-2">
            <span className="amazon-price">${price}</span>
            {originalPrice && originalPrice > price && (
              <span className="text-sm text-gray-500 line-through">
                ${originalPrice}
              </span>
            )}
          </div>
        </div>

        {/* Prime and Shipping */}
        <div className="flex flex-col space-y-1 mb-3">
          {prime && (
            <div className="flex items-center">
              <span className="text-blue-500 text-xs font-bold bg-blue-100 px-2 py-1 rounded">
                prime
              </span>
            </div>
          )}
          {freeShipping && (
            <div className="text-xs text-green-600">
              FREE Shipping
            </div>
          )}
        </div>

        {/* Category Badge */}
        {category && (
          <div className="text-xs text-gray-500 mb-2">
            in {category}
          </div>
        )}
      </Link>

      {/* Add to Cart Button */}
      <button
        onClick={(e) => {
          e.preventDefault();
          addItem(product);
        }}
        className="w-full amazon-btn-cart text-xs py-1 mt-2"
      >
        Add to Cart
      </button>
    </div>
  );
}
